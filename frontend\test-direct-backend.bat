@echo off
echo 测试直接连接后端服务...

echo 1. 获取后端服务IP地址...
for /f "tokens=*" %%i in ('docker inspect cont-rtm-backend --format "{{.NetworkSettings.IPAddress}}"') do set BACKEND_IP=%%i
echo 后端服务IP: %BACKEND_IP%

echo 2. 更新nginx配置为直接连接后端...
powershell -Command "(Get-Content nginx.conf) -replace 'proxy_pass http://.*:909[01];', 'proxy_pass http://%BACKEND_IP%:9091;' | Set-Content nginx.conf"

echo 3. 重新构建前端容器...
docker stop rtm-web
docker rm rtm-web
docker build -t rtm-web .
docker run -d -p 80:80 --name rtm-web rtm-web

echo 4. 等待容器启动...
timeout /t 5 /nobreak

echo 5. 测试后端服务连通性...
docker exec rtm-web nc -zv %BACKEND_IP% 9091

echo 6. 测试验证码接口...
docker exec rtm-web wget -qO- --timeout=10 "http://%BACKEND_IP%:9091/auth/captcha?id=test&t=123" 2>nul
if %errorlevel% neq 0 (
    echo 后端服务验证码接口测试失败！
    echo 检查后端服务日志:
    docker logs --tail 10 cont-rtm-backend
) else (
    echo 后端服务验证码接口测试成功！
)

echo.
echo 现在请访问 http://localhost 测试验证码功能
echo 如果直接连接后端成功，说明问题在网关配置
echo 如果仍然失败，说明问题在后端服务

pause
