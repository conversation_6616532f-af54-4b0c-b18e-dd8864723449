@echo off
echo 正在诊断和修复502错误...

echo.
echo 1. 检查容器状态...
docker ps -a | findstr rtm

echo.
echo 2. 检查网关服务IP地址...
for /f "tokens=*" %%i in ('docker inspect cont-rtm-gateway --format "{{.NetworkSettings.IPAddress}}"') do set GATEWAY_IP=%%i
echo 网关服务IP地址: %GATEWAY_IP%

echo.
echo 3. 检查网关服务是否响应...
docker exec rtm-web nc -zv cont-rtm-gateway 9090 2>nul
if %errorlevel% neq 0 (
    echo 警告: 无法连接到网关服务！
    echo 检查网关服务日志...
    docker logs --tail 10 cont-rtm-gateway
) else (
    echo 网关服务连接正常
)

echo.
echo 4. 重新构建前端容器（使用容器名称代理）...
docker stop rtm-web 2>nul
docker rm rtm-web 2>nul

echo 5. 构建新的前端镜像...
docker build -t rtm-web .
if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)

echo 6. 启动前端容器（连接到网关网络）...
docker run -d -p 80:80 --name rtm-web --link cont-rtm-gateway:cont-rtm-gateway rtm-web
if %errorlevel% neq 0 (
    echo 容器启动失败！
    pause
    exit /b 1
)

echo.
echo 7. 等待容器启动...
timeout /t 5 /nobreak >nul

echo 8. 测试API连接...
echo 请在浏览器中访问: http://localhost
echo 然后尝试获取验证码

echo.
echo 如果仍有问题，请检查以下日志:
echo - 前端nginx日志: docker logs rtm-web
echo - 网关服务日志: docker logs cont-rtm-gateway
echo - 后端服务日志: docker logs cont-rtm-backend

pause
