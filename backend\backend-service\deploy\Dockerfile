
#FROM openjdk:17-jdk-slim

#COPY backend-service-1.0.0.jar /app/backend-service-1.0.0.jar

#ENTRYPOINT ["java", "-jar", "/app/backend-service-1.0.0.jar","-Dspring.config.location=file:/app/config/application.yml", "backend-service-1.0.0.jar"]

#EXPOSE 9091


FROM openjdk:17-jdk-slim
WORKDIR /app
COPY backend-service-1.0.0.jar /app/
# 复制配置文件到容器内
COPY application.yml /app/config/

# 指定配置文件位置
CMD ["java", "-jar", "-Dspring.config.location=file:/app/config/application.yml", "backend-service-1.0.0.jar"]

EXPOSE 9091