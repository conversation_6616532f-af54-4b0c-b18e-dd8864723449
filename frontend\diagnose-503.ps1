# Diagnose 503 error
Write-Host "Diagnosing 503 error..." -ForegroundColor Green

Write-Host "`n1. Check all container status..." -ForegroundColor Yellow
docker ps -a | Select-String "(rtm|nacos)"

Write-Host "`n2. Check gateway service logs..." -ForegroundColor Yellow
Write-Host "=== Gateway Service Recent Logs ===" -ForegroundColor Cyan
docker logs --tail 20 cont-rtm-gateway

Write-Host "`n3. Check backend service logs..." -ForegroundColor Yellow
Write-Host "=== Backend Service Recent Logs ===" -ForegroundColor Cyan
docker logs --tail 20 cont-rtm-backend

Write-Host "`n4. Get gateway service IP..." -ForegroundColor Yellow
$gatewayIP = docker inspect cont-rtm-gateway --format "{{.NetworkSettings.IPAddress}}"
Write-Host "Gateway IP: $gatewayIP" -ForegroundColor Green

Write-Host "`n5. Test gateway service port..." -ForegroundColor Yellow
docker exec rtm-web nc -zv $gatewayIP 9090

Write-Host "`n6. Test gateway health endpoint..." -ForegroundColor Yellow
$healthResult = docker exec rtm-web wget -qO- --timeout=5 "http://$gatewayIP:9090/actuator/health" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "Gateway health check: SUCCESS" -ForegroundColor Green
    Write-Host $healthResult
} else {
    Write-Host "Gateway health check: FAILED" -ForegroundColor Red
}

Write-Host "`n7. Test Nacos connection..." -ForegroundColor Yellow
docker exec cont-rtm-gateway nc -zv ********** 8848

Write-Host "`n8. Direct test captcha API..." -ForegroundColor Yellow
$captchaResult = docker exec rtm-web wget -qO- --timeout=10 "http://$gatewayIP:9090/api/auth/captcha?id=test&t=123" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "Direct captcha API access: SUCCESS" -ForegroundColor Green
} else {
    Write-Host "Direct captcha API access: FAILED" -ForegroundColor Red
    Write-Host "This might be a routing configuration issue"
}

Write-Host "`n9. Check service registration..." -ForegroundColor Yellow
$serviceList = docker exec cont-rtm-backend curl -s "http://**********:8848/nacos/v1/ns/instance/list?serviceName=rtm-backend" 2>$null
if ($serviceList) {
    Write-Host "Backend service registration:" -ForegroundColor Green
    Write-Host $serviceList
} else {
    Write-Host "Failed to get service registration info" -ForegroundColor Red
}

Write-Host "`n=== Diagnosis Complete ===" -ForegroundColor Green
Write-Host "Please analyze the output above to identify the issue"
Read-Host "Press Enter to continue"
