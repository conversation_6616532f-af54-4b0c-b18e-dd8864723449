@echo off
echo 快速修复502错误...

echo 1. 检查网关服务状态...
docker ps | findstr cont-rtm-gateway
if %errorlevel% neq 0 (
    echo 错误: 网关服务容器未运行！
    echo 请先启动网关服务:
    echo docker start cont-rtm-gateway
    pause
    exit /b 1
)

echo 2. 获取网关服务IP地址...
for /f "tokens=*" %%i in ('docker inspect cont-rtm-gateway --format "{{.NetworkSettings.IPAddress}}"') do set GATEWAY_IP=%%i
echo 网关服务IP: %GATEWAY_IP%

if "%GATEWAY_IP%"=="" (
    echo 错误: 无法获取网关服务IP地址！
    pause
    exit /b 1
)

echo 3. 更新nginx配置文件...
powershell -Command "(Get-Content nginx.conf) -replace 'proxy_pass http://.*:9090;', 'proxy_pass http://%GATEWAY_IP%:9090;' | Set-Content nginx.conf"

echo 4. 重新构建前端容器...
docker stop rtm-web 2>nul
docker rm rtm-web 2>nul
docker build -t rtm-web .

echo 5. 启动前端容器...
docker run -d -p 80:80 --name rtm-web rtm-web

echo 6. 等待启动完成...
timeout /t 3 /nobreak >nul

echo 修复完成！请访问 http://localhost 测试验证码功能
echo.
echo 如果还有问题，请检查日志:
echo docker logs rtm-web
echo docker logs cont-rtm-gateway

pause
