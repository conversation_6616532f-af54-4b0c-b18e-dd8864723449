@echo off
echo Fixing gateway routing issue...

echo 1. Update Nacos gateway configuration...
curl -X POST "http://**********:8848/nacos/v1/cs/configs" ^
  -d "dataId=rtm-gateway.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "content=spring:%0A  cloud:%0A    gateway:%0A      routes:%0A        - id: backend-service%0A          uri: http://**********:9091%0A          predicates:%0A            - Path=/api/**%0A          filters:%0A            - StripPrefix=1%0A            - AddRequestHeader=X-Gateway-Source, rtm-gateway%0A          metadata:%0A            resource-name: backend-service%0A    nacos:%0A      discovery:%0A        server-addr: **********:8848%0A        enabled: true%0A        service: rtm-gateway%0A    sentinel:%0A      transport:%0A        dashboard: localhost:8080%0A        port: 8719%0A      http-method-specify: true%0A      eager: true%0Alogging:%0A  level:%0A    com.dz.ms.gateway: DEBUG%0A    org.springframework.cloud.gateway: DEBUG%0Amanagement:%0A  endpoints:%0A    web:%0A      exposure:%0A        include: '*'%0A  endpoint:%0A    health:%0A      show-details: always"

if %errorlevel% equ 0 (
    echo Gateway configuration updated successfully!
) else (
    echo Failed to update gateway configuration!
    pause
    exit /b 1
)

echo 2. Restart gateway service to reload configuration...
docker restart cont-rtm-gateway

echo 3. Wait for gateway service to start...
timeout /t 15 /nobreak

echo 4. Check gateway service logs...
docker logs --tail 10 cont-rtm-gateway

echo 5. Test the API endpoint...
timeout /t 5 /nobreak
echo Testing captcha API...
curl -I "http://localhost/api/auth/captcha?id=test&t=123"

echo.
echo Fix completed! Please test the captcha functionality in browser.
echo If still not working, check logs:
echo - Gateway: docker logs cont-rtm-gateway
echo - Backend: docker logs cont-rtm-backend
echo - Frontend: docker logs rtm-web

pause
