@echo off
echo Diagnosing 503 error...

echo.
echo 1. Check all container status...
docker ps -a | findstr -E "(rtm|nacos)"

echo.
echo 2. Check gateway service logs...
echo === Gateway Service Recent Logs ===
docker logs --tail 20 cont-rtm-gateway

echo.
echo 3. Check backend service logs...
echo === Backend Service Recent Logs ===
docker logs --tail 20 cont-rtm-backend

echo.
echo 4. Test gateway service health...
for /f "tokens=*" %%i in ('docker inspect cont-rtm-gateway --format "{{.NetworkSettings.IPAddress}}"') do set GATEWAY_IP=%%i
echo Gateway IP: %GATEWAY_IP%

echo Testing gateway service port...
docker exec rtm-web nc -zv %GATEWAY_IP% 9090

echo.
echo 5. Test gateway HTTP response...
docker exec rtm-web wget -qO- --timeout=5 http://%GATEWAY_IP%:9090/actuator/health 2>nul
if %errorlevel% neq 0 (
    echo Gateway health check failed!
) else (
    echo Gateway health check success
)

echo.
echo 6. Check Nacos connection...
echo Testing Nacos connection...
docker exec cont-rtm-gateway nc -zv ********** 8848

echo.
echo 7. Direct test captcha API...
echo Trying to access gateway captcha API directly...
docker exec rtm-web wget -qO- --timeout=10 "http://%GATEWAY_IP%:9090/api/auth/captcha?id=test&t=123" 2>nul
if %errorlevel% neq 0 (
    echo Direct captcha API access failed!
    echo This might be a routing configuration issue
) else (
    echo Direct captcha API access success
)

echo.
echo 8. Check service registration...
echo Check if backend service is registered in Nacos...
docker exec cont-rtm-backend curl -s "http://**********:8848/nacos/v1/ns/instance/list?serviceName=rtm-backend" 2>nul

echo.
echo === Diagnosis Complete ===
echo Please analyze the output above to identify the issue
pause
