@echo off
echo 诊断503错误...

echo.
echo 1. 检查所有容器状态...
docker ps -a | findstr -E "(rtm|nacos)"

echo.
echo 2. 检查网关服务日志...
echo === 网关服务最近日志 ===
docker logs --tail 20 cont-rtm-gateway

echo.
echo 3. 检查后端服务日志...
echo === 后端服务最近日志 ===
docker logs --tail 20 cont-rtm-backend

echo.
echo 4. 测试网关服务健康状态...
for /f "tokens=*" %%i in ('docker inspect cont-rtm-gateway --format "{{.NetworkSettings.IPAddress}}"') do set GATEWAY_IP=%%i
echo 网关IP: %GATEWAY_IP%

echo 测试网关服务端口...
docker exec rtm-web nc -zv %GATEWAY_IP% 9090

echo.
echo 5. 测试网关服务HTTP响应...
docker exec rtm-web wget -qO- --timeout=5 http://%GATEWAY_IP%:9090/actuator/health 2>nul
if %errorlevel% neq 0 (
    echo 网关服务健康检查失败！
) else (
    echo 网关服务健康检查成功
)

echo.
echo 6. 检查Nacos连接...
echo 测试Nacos连接...
docker exec cont-rtm-gateway nc -zv ********** 8848

echo.
echo 7. 直接测试验证码接口...
echo 尝试直接访问网关的验证码接口...
docker exec rtm-web wget -qO- --timeout=10 "http://%GATEWAY_IP%:9090/api/auth/captcha?id=test&t=123" 2>nul
if %errorlevel% neq 0 (
    echo 直接访问验证码接口失败！
    echo 这可能是路由配置问题
) else (
    echo 直接访问验证码接口成功
)

echo.
echo 8. 检查服务注册情况...
echo 检查后端服务是否在Nacos中注册...
docker exec cont-rtm-backend curl -s "http://**********:8848/nacos/v1/ns/instance/list?serviceName=rtm-backend" 2>nul

echo.
echo === 诊断完成 ===
echo 请根据上面的输出信息判断问题所在
pause
