<template>
  <div class="page-container">
    <el-card>
      <div style="margin-bottom: 16px; display: flex; gap: 8px; align-items: center;">
        <el-button type="success" @click="openAdd">新增菜单</el-button>
      </div>
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="菜单名称" width="180"></el-table-column>
        <el-table-column prop="type" label="类型" width="80">
           <template #default="scope">
            <el-tag v-if="scope.row.type === 'M'">目录</el-tag>
            <el-tag v-else-if="scope.row.type === 'C'" type="success">菜单</el-tag>
            <el-tag v-else-if="scope.row.type === 'F'" type="info">按钮</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" width="80"></el-table-column>
        <el-table-column prop="permission" label="权限标识"></el-table-column>
        <el-table-column prop="path" label="路由路径"></el-table-column>
         <el-table-column prop="component" label="组件路径"></el-table-column>
        <el-table-column prop="orderNum" label="排序" width="80"></el-table-column>
         <el-table-column prop="visible" label="显示">
           <template #default="scope">
            {{ scope.row.visible === 1 ? '显示' : '隐藏' }}
          </template>
        </el-table-column>
         <el-table-column prop="status" label="状态">
           <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'info'">
              {{ scope.row.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="openAdd(scope.row)" v-if="scope.row.type === 'M' || scope.row.type === 'C'">新增</el-button>
            <el-button size="small" @click="openEdit(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="上级菜单" prop="parentId">
           <el-tree-select
              v-model="form.parentId"
              :data="menuTreeData"
              :props="{ value: 'id', label: 'name' }"
              node-key="id"
              check-strictly
              clearable
              placeholder="选择上级菜单"
            />
        </el-form-item>
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="M">目录</el-radio>
            <el-radio label="C">菜单</el-radio>
            <el-radio label="F">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="form.name" autocomplete="off" />
        </el-form-item>
        <el-form-item label="路由路径" prop="path" v-if="form.type !== 'F'">
          <el-input v-model="form.path" />
        </el-form-item>
         <el-form-item label="组件路径" prop="component" v-if="form.type === 'C'">
          <el-input v-model="form.component" />
        </el-form-item>
         <el-form-item label="权限标识" prop="permission">
          <el-input v-model="form.permission" />
        </el-form-item>
         <el-form-item label="图标" prop="icon" v-if="form.type !== 'F'">
           <el-input v-model="form.icon" />
         </el-form-item>
         <el-form-item label="排序" prop="orderNum">
           <el-input-number v-model="form.orderNum" :min="0" />
         </el-form-item>
         <el-form-item label="是否显示" prop="visible" v-if="form.type !== 'F'">
           <el-radio-group v-model="form.visible">
            <el-radio :label="1">显示</el-radio>
            <el-radio :label="0">隐藏</el-radio>
          </el-radio-group>
         </el-form-item>
         <el-form-item label="菜单状态" prop="status">
           <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
         </el-form-item>

      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage,ElTreeSelect, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus';
import { // Import menu API functions later
  getMenuTree, addMenu, updateMenu, deleteMenu } from '../../api/menu';
import type { Menu } from '../../api/menu';; // Import Menu interface

const tableData = ref<Menu[]>([]);
const menuTreeData = ref<Menu[]>([]);

const dialogVisible = ref(false);
const dialogTitle = ref('');
const form = reactive<Menu>({ // Initialize with default values or null where appropriate
  parentId: 0, // Root level parentId
  name: '',
  path: '',
  component: '',
  icon: '',
  type: 'M', // Default to Directory
  permission: '',
  orderNum: 0,
  visible: 1,
  status: 1,
});
const formRef = ref<FormInstance>();

const rules: FormRules = {
  name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
  path: [{ required: true, message: '请输入路由路径', trigger: 'blur', trigger: 'change' }],
  permission: [{ required: true, message: '请输入权限标识', trigger: 'blur' }],
  // Add more rules based on type if needed (e.g., component required for type C)
};

// Function to fetch menu data (tree structure)
async function fetchData() {
  try {
    // TODO: Replace with actual getMenuTree API call
    console.log("Fetching menu tree...");
     const res = await getMenuTree(); // Use actual API call
    tableData.value = res.data;
    menuTreeData.value = buildTreeSelectData(res.data);
  } catch (error) {
    console.error("获取菜单列表失败:", error);
    tableData.value = [];
    menuTreeData.value = [];
    ElMessage.error('获取菜单列表失败');
  }
}

// Helper function to build data for el-tree-select (flattened with hierarchy indication)
function buildTreeSelectData(menuList: Menu[]): Menu[] {
  const result: Menu[] = [];
  function flatten(menus: Menu[], level: number = 0, prefix: string = '') {
    menus.forEach(menu => {
      const currentName = prefix + menu.name;
       // Add current menu item
      result.push({ ...menu, name: currentName });
      // Recursively add children
      if (menu.children && menu.children.length > 0) {
        flatten(menu.children, level + 1, prefix + '  '); // Add indentation
      }
    });
  }
  // Assuming top-level menus have parentId 0 or null
  const topLevelMenus = menuList.filter(menu => menu.parentId === 0 || menu.parentId === null);
  flatten(topLevelMenus);
  return result;
}

function openAdd(row?: Menu) {
  dialogTitle.value = '新增菜单';
  // Reset form and set parentId if adding a child menu
  Object.assign(form, { 
    id: undefined,
    parentId: row ? row.id : 0, // Default to root if no row provided
    name: '',
    path: '',
    component: '',
    icon: '',
    type: 'M', // Default type for new menu
    permission: '',
    orderNum: 0,
    visible: 1,
    status: 1,
  });
  dialogVisible.value = true;
}

function openEdit(row: Menu) {
  dialogTitle.value = '编辑菜单';
  // Assign row data to form, handle parentId 0/null for root
  Object.assign(form, { ...row, parentId: row.parentId === null ? 0 : row.parentId });
   // Ensure type is correctly assigned if needed, although Object.assign should handle this
  dialogVisible.value = true;
}

async function handleSubmit() {
  formRef.value?.validate(async (valid) => {
    if (!valid) return;
    try {
      if (!form.id) {
        // Add menu
         // TODO: Replace with actual addMenu API call
        await addMenu(form); // Use actual API call
        ElMessage.success('新增成功');
      } else {
        // Update menu
         // TODO: Replace with actual updateMenu API call
         await updateMenu(form.id, form); // Use actual API call
        ElMessage.success('编辑成功');
      }
      dialogVisible.value = false;
      fetchData(); // Refresh list
    } catch (error) {
      console.error("提交菜单信息失败:", error);
      ElMessage.error('操作失败');
    }
  });
}

async function handleDelete(id?: number) {
  if (!id) return;
  
  ElMessageBox.confirm(
    '确定要删除该菜单吗？如果存在子菜单，将无法删除',
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        const res = await deleteMenu(id);
        if (res && res.code === 0) {
          ElMessage.success('删除成功');
          fetchData(); // Refresh list
        } else {
          ElMessage.error(res?.msg || '删除失败，可能存在子菜单');
        }
      } catch (error) {
        console.error("删除菜单失败:", error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 用户取消删除操作
      ElMessage.info('已取消删除');
    });
}

onMounted(fetchData);
</script>

<style scoped>
.menu-list {
  padding: 24px;
}

/* Adjusting tree-select width */
.el-form-item__content .el-select {
    width: 100%;
}
</style> 