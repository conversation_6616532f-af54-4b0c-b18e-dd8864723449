@echo off
echo 检查和配置Nacos...

echo 1. 检查Nacos服务状态...
docker ps | findstr nacos
if %errorlevel% neq 0 (
    echo 错误: Nacos服务未运行！
    echo 请先启动Nacos服务
    pause
    exit /b 1
)

echo.
echo 2. 检查网关配置是否存在...
curl -s "http://**********:8848/nacos/v1/cs/configs?dataId=rtm-gateway.yaml&group=DEFAULT_GROUP" > temp_config.txt
findstr "code" temp_config.txt >nul
if %errorlevel% equ 0 (
    echo 网关配置不存在，正在创建...
    goto create_config
) else (
    echo 网关配置已存在
    type temp_config.txt
    goto end
)

:create_config
echo 3. 创建网关配置...
curl -X POST "http://**********:8848/nacos/v1/cs/configs" ^
  -d "dataId=rtm-gateway.yaml" ^
  -d "group=DEFAULT_GROUP" ^
  -d "content=spring:%0A  cloud:%0A    gateway:%0A      routes:%0A        - id: backend-service%0A          uri: lb://rtm-backend%0A          predicates:%0A            - Path=/api/**%0A          filters:%0A            - StripPrefix=1%0A    nacos:%0A      discovery:%0A        server-addr: **********:8848%0A        enabled: true%0A        service: rtm-gateway"

if %errorlevel% equ 0 (
    echo 网关配置创建成功！
) else (
    echo 网关配置创建失败！
)

:end
del temp_config.txt 2>nul

echo.
echo 4. 重启网关服务以加载新配置...
docker restart cont-rtm-gateway

echo 等待网关服务启动...
timeout /t 15 /nobreak

echo.
echo 5. 检查网关服务日志...
docker logs --tail 10 cont-rtm-gateway

echo.
echo 配置检查完成！
pause
