package com.dz.ms.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * CORS配置
 * <AUTHOR>
 * @date 2025-05-28
 * @version 1.0.0
 */
@Configuration
public class CorsConfig {

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true); // 允许携带cookie
        // 设置允许跨域的来源，根据你的前端实际部署地址修改
        config.addAllowedOrigin("http://localhost:5173"); // 开发环境
        config.addAllowedOrigin("http://localhost"); // Docker部署环境
        config.addAllowedOrigin("http://localhost:80"); // Docker部署环境（显式端口）
        config.addAllowedHeader("*"); // 允许所有请求头
        config.addAllowedMethod("*"); // 允许所有请求方法（GET, POST, PUT, DELETE等）
        source.registerCorsConfiguration("/**", config); // 对所有路径生效
        return new CorsFilter(source);
    }
} 