server:
  port: 9090

spring:
  application:
    name: rtm-gateway
  config:
    import: "optional:nacos:rtm-gateway.yaml"
  cloud:
    nacos:
      server-addr: **********:8848
#放到nacos中了（要注意Nacos中不能有注释，要去掉注释复制过去）
#    sentinel:
#      transport:
#        # sentinel控制台地址
#        dashboard: localhost:8080
#        # 指定应用与Sentinel控制台交互的端口，应用本地会起一个该端口占用的HttpServer
#        port: 8719
#      http-method-specify: true #开启请求方式前缀
#      eager: true  # 启用饥饿加载模式，服务启动时就会自动注册到 Dashboard

    
 